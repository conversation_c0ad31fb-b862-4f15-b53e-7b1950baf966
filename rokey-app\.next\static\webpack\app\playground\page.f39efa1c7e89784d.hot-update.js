"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_ChatroomOrchestrator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ChatroomOrchestrator */ \"(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            // Check if this is an orchestration started error\n            if (err.message && err.message.includes(\"ORCHESTRATION_STARTED:\")) {\n                const executionId = err.message.split(\"ORCHESTRATION_STARTED:\")[1];\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] Detected orchestration start, execution ID:\", executionId);\n                // Set orchestration state\n                setOrchestrationExecutionId(executionId);\n                setShowOrchestration(true);\n                // Don't show this as an error - it's expected behavior\n                return;\n            }\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1964,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1965,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1969,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1970,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1961,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1981,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 1983,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1975,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1990,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1989,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1988,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1974,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1960,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1998,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2005,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1999,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1997,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 1958,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 1957,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1953,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2024,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2025,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2023,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2042,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2046,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2049,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2045,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2041,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2056,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2055,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2054,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2033,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2031,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2022,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2021,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2020,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full max-w-4xl h-full overflow-y-auto px-6\",\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2078,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2077,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2093,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2096,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2097,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2098,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2095,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2094,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2092,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2090,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2114,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2113,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2112,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-[65%] relative \".concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2132,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2144,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2139,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2131,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2152,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2159,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2151,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2172,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2186,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2187,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2181,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2193,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2194,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2189,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2180,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2197,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2171,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2208,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2216,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2224,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2168,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2119,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2241,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2240,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2239,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2107,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatroomOrchestrator__WEBPACK_IMPORTED_MODULE_6__.ChatroomOrchestrator, {\n                                                        executionId: orchestrationExecutionId,\n                                                        onComplete: (result)=>{\n                                                            console.log(\"\\uD83C\\uDF89 [CHATROOM ORCHESTRATION] Completed:\", result);\n                                                            // Add the final result as a message\n                                                            const finalMessage = {\n                                                                id: Date.now().toString() + \"-orchestration-final\",\n                                                                role: \"assistant\",\n                                                                content: [\n                                                                    {\n                                                                        type: \"text\",\n                                                                        text: result\n                                                                    }\n                                                                ]\n                                                            };\n                                                            setMessages((prevMessages)=>[\n                                                                    ...prevMessages,\n                                                                    finalMessage\n                                                                ]);\n                                                            // Hide orchestration UI\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                            // Save final message\n                                                            if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                                saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                    console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                                });\n                                                            }\n                                                        },\n                                                        onError: (error)=>{\n                                                            console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                            setError(\"Orchestration error: \".concat(error));\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2074,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2069,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2068,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2311,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2310,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2305,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2304,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2067,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2017,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2332,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2331,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2334,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2330,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2329,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2345,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2346,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2344,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2350,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2362,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2367,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2374,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2368,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2361,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2377,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2360,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2358,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2413,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2415,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2401,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2422,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2457,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2456,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2460,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2321,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1948,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2486,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2485,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2500,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2499,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2494,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2482,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2512,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2511,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2507,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2506,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2525,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2526,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2524,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2522,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2534,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2533,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2532,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2537,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2538,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2531,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2543,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2520,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2560,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2559,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2558,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2557,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2571,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2572,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2570,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2569,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2480,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2474,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2594,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2593,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2588,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2585,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1946,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"dQtgRDvqik5z8cOOvJCbcR0Oe/c=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});