"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx":
/*!*************************************************!*\
  !*** ./src/components/ChatroomOrchestrator.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatroomOrchestrator: function() { return /* binding */ ChatroomOrchestrator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/specialistPersonalities */ \"(app-pages-browser)/./src/config/specialistPersonalities.ts\");\n/* harmony import */ var _SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SpecialistMessage */ \"(app-pages-browser)/./src/components/SpecialistMessage.tsx\");\n/* harmony import */ var _ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModeratorMessage */ \"(app-pages-browser)/./src/components/ModeratorMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatroomOrchestrator auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatroomOrchestrator = (param)=>{\n    let { executionId, onComplete, onError } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingIndicators, setTypingIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [activeSpecialists, setActiveSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use the orchestration stream hook\n    const { events, isConnected, error: streamError, lastEvent } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Log component mount and connection status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDE80 ChatroomOrchestrator mounted with executionId:\", executionId);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDD0C Connection status changed:\", isConnected ? \"CONNECTED\" : \"DISCONNECTED\");\n    }, [\n        isConnected\n    ]);\n    // Auto-scroll to bottom when new messages arrive\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    // Convert orchestration events to chatroom messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lastEvent) return;\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCE8 New event received:\", lastEvent.type);\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCCB Event data:\", lastEvent);\n        const convertEventToMessage = (event)=>{\n            const personality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"moderator\");\n            switch(event.type){\n                case \"orchestration_started\":\n                    var _event_data_originalPrompt;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: 'Welcome team! We have a new request to tackle: \"'.concat((_event_data_originalPrompt = event.data.originalPrompt) === null || _event_data_originalPrompt === void 0 ? void 0 : _event_data_originalPrompt.substring(0, 200), '...\"'),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"task_decomposed\":\n                    var _event_data_roles;\n                    const specialists = ((_event_data_roles = event.data.roles) === null || _event_data_roles === void 0 ? void 0 : _event_data_roles.map((r)=>{\n                        var _getSpecialistPersonality;\n                        return ((_getSpecialistPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(r.roleId)) === null || _getSpecialistPersonality === void 0 ? void 0 : _getSpecialistPersonality.name) || r.roleId;\n                    }).join(\", \")) || \"specialists\";\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"I've analyzed the request and I'm assigning this to: \".concat(specialists, \". Let's collaborate to deliver an amazing result! \\uD83D\\uDE80\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"step_assigned\":\n                    var _event_data_step;\n                    const assignedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!assignedPersonality) return null;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"@\".concat(assignedPersonality.name, \", you're up! Here's what I need: \").concat(((_event_data_step = event.data.step) === null || _event_data_step === void 0 ? void 0 : _event_data_step.prompt) || \"Handle your specialty for this request.\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_started\":\n                    const workingPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!workingPersonality) return null;\n                    const acknowledgment = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(workingPersonality.communicationStyle.acknowledgment);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: workingPersonality.name,\n                        messageType: \"acknowledgment\",\n                        content: acknowledgment,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_progress\":\n                    const progressPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!progressPersonality) return null;\n                    const workingIndicator = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(progressPersonality.communicationStyle.workingIndicators);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: progressPersonality.name,\n                        messageType: \"work_update\",\n                        content: workingIndicator,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number,\n                            progress: event.data.progress,\n                            isStreaming: true\n                        }\n                    };\n                case \"step_completed\":\n                    const completedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!completedPersonality) return null;\n                    const completionPhrase = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(completedPersonality.communicationStyle.completionPhrases);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: completedPersonality.name,\n                        messageType: \"final_output\",\n                        content: \"\".concat(completionPhrase, \"\\n\\n\").concat(event.data.output || \"Work completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"synthesis_started\":\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"Outstanding teamwork everyone! \\uD83C\\uDF89 Let me compile all your excellent work into a final, cohesive solution.\",\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 999\n                        }\n                    };\n                case \"orchestration_completed\":\n                    setIsComplete(true);\n                    setFinalResult(event.data.finalResult || \"\");\n                    if (onComplete && event.data.finalResult) {\n                        onComplete(event.data.finalResult);\n                    }\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"final_output\",\n                        content: \"\\uD83C\\uDF89 Brilliant collaboration team! Here's our final solution:\\n\\n\".concat(event.data.finalResult || \"Task completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 1000\n                        }\n                    };\n                default:\n                    return null;\n            }\n        };\n        const newMessage = convertEventToMessage(lastEvent);\n        if (newMessage) {\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] ✅ Created new message:\", newMessage.messageType);\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDC64 From:\", newMessage.senderName);\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCAD Content preview:\", newMessage.content.substring(0, 100) + \"...\");\n            setMessages((prev)=>{\n                // Avoid duplicates\n                if (prev.some((msg)=>msg.id === newMessage.id)) {\n                    console.log(\"\\uD83D\\uDCAC [CHATROOM] ⚠️ Duplicate message detected, skipping\");\n                    return prev;\n                }\n                console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCDD Adding message to chat, total messages:\", prev.length + 1);\n                return [\n                    ...prev,\n                    newMessage\n                ];\n            });\n            // Update active specialists\n            if (newMessage.sender !== \"moderator\" && !activeSpecialists.includes(newMessage.sender)) {\n                setActiveSpecialists((prev)=>[\n                        ...prev,\n                        newMessage.sender\n                    ]);\n            }\n        }\n        // Handle typing indicators\n        if (lastEvent.type === \"step_started\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>new Set([\n                    ...prev,\n                    lastEvent.role_id\n                ]));\n        } else if (lastEvent.type === \"step_completed\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(lastEvent.role_id);\n                return newSet;\n            });\n        }\n    }, [\n        lastEvent,\n        activeSpecialists,\n        onComplete\n    ]);\n    // Handle stream errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (streamError && onError) {\n            onError(streamError);\n        }\n    }, [\n        streamError,\n        onError\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg border border-gray-200 h-[600px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"AI Team Collaboration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 text-sm\",\n                                    children: isComplete ? \"Session Complete\" : isConnected ? \"Live Session\" : \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        activeSpecialists.length + 1,\n                                        \" members\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\",\n                children: [\n                    messages.map((message)=>message.sender === \"moderator\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__.ModeratorMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__.SpecialistMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, undefined)),\n                    Array.from(typingIndicators).map((roleId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__.TypingIndicator, {\n                            roleId: roleId\n                        }, \"typing-\".concat(roleId), false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-gray-100 rounded-b-lg border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isComplete ? \"Collaboration complete!\" : \"\".concat(messages.length, \" messages • \").concat(activeSpecialists.length, \" specialists active\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-red-500\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isConnected ? \"Connected\" : \"Disconnected\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatroomOrchestrator, \"2PPNx10LNSyyiUsY9MtrcN/e/2c=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = ChatroomOrchestrator;\nvar _c;\n$RefreshReg$(_c, \"ChatroomOrchestrator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx\n"));

/***/ })

});