"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_ChatroomOrchestrator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ChatroomOrchestrator */ \"(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"\\uD83D\\uDEA8 [PLAYGROUND] API call error:\", err);\n            console.log(\"\\uD83D\\uDD0D [PLAYGROUND] Error message content:\", err.message);\n            // Check if this is an orchestration started error\n            if (err.message && err.message.includes(\"ORCHESTRATION_STARTED:\")) {\n                const executionId = err.message.split(\"ORCHESTRATION_STARTED:\")[1];\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] ✅ Detected orchestration start!\");\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] \\uD83D\\uDCCB Execution ID:\", executionId);\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] \\uD83D\\uDE80 Setting up chatroom UI...\");\n                // Set orchestration state\n                setOrchestrationExecutionId(executionId);\n                setShowOrchestration(true);\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] ✅ Chatroom UI activated!\");\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] \\uD83C\\uDFAF showOrchestration:\", true);\n                console.log(\"\\uD83C\\uDFAC [ORCHESTRATION] \\uD83C\\uDD94 orchestrationExecutionId:\", executionId);\n                // Don't show this as an error - it's expected behavior\n                return;\n            }\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1971,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1972,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1976,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1977,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1968,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1988,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 1990,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1982,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1997,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1996,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1995,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1981,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1967,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2005,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2012,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2006,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2004,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 1965,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 1964,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1960,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2031,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2032,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2030,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2049,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2053,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2056,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2052,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2048,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2063,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2062,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2061,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2040,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2038,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2029,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2028,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2027,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full max-w-4xl h-full overflow-y-auto px-6\",\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2085,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2084,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2100,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2103,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2104,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2105,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2102,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2101,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2099,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2097,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2121,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2120,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2119,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-[65%] relative \".concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2139,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2151,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2146,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2138,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2159,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2166,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2158,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2179,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2193,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2194,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2188,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2200,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2201,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2196,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2187,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2204,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2178,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2215,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2223,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2231,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2175,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2126,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2248,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2247,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2246,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2114,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatroomOrchestrator__WEBPACK_IMPORTED_MODULE_6__.ChatroomOrchestrator, {\n                                                        executionId: orchestrationExecutionId,\n                                                        onComplete: (result)=>{\n                                                            console.log(\"\\uD83C\\uDF89 [CHATROOM ORCHESTRATION] Completed:\", result);\n                                                            // Add the final result as a message\n                                                            const finalMessage = {\n                                                                id: Date.now().toString() + \"-orchestration-final\",\n                                                                role: \"assistant\",\n                                                                content: [\n                                                                    {\n                                                                        type: \"text\",\n                                                                        text: result\n                                                                    }\n                                                                ]\n                                                            };\n                                                            setMessages((prevMessages)=>[\n                                                                    ...prevMessages,\n                                                                    finalMessage\n                                                                ]);\n                                                            // Hide orchestration UI\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                            // Save final message\n                                                            if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                                saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                    console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                                });\n                                                            }\n                                                        },\n                                                        onError: (error)=>{\n                                                            console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                            setError(\"Orchestration error: \".concat(error));\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2081,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2076,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2075,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2318,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2317,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2312,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2311,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2074,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2024,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2339,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2338,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2337,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2353,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2351,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2357,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2369,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2374,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2381,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2375,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2368,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2384,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2367,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2365,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2397,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2420,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2422,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2408,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2430,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2429,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2464,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2463,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2467,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2455,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2395,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2333,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2328,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1955,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2493,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2492,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2496,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2507,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2506,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2501,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2489,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2519,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2521,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2532,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2533,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2531,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2529,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2541,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2540,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2539,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2544,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2538,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2550,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2527,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2567,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2566,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2565,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2564,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2578,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2579,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2577,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2576,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2487,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2481,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2601,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2600,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2595,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2592,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1953,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"dQtgRDvqik5z8cOOvJCbcR0Oe/c=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});