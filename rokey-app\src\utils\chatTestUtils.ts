// Test utilities for chat-style orchestration
import { 
  createModeratorMessage, 
  createSpecialistMessage, 
  emitChatMessage,
  getSpecialistPersonality,
  ChatMessage,
  ChatAttachment
} from './orchestrationUtils';

// Test function to emit sample chat messages
export async function emitSampleChatMessages(executionId: string): Promise<void> {
  console.log('🧪 [Chat Test] Emitting sample chat messages for execution:', executionId);
  
  // Delay helper
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
  
  try {
    // 1. Moderator opens the session
    const openingMessage = createModeratorMessage(
      "🎬 Welcome team! We have a new request from our user. Let me break this down and assign the right specialists.",
      'announcement'
    );
    await emitChatMessage(executionId, openingMessage);
    await delay(1000);

    // 2. Moderator assigns tasks
    const assignmentMessage = createModeratorMessage(
      "📋 I'm assigning this to @Alex Code for the technical implementation and @Maya Design for the UI/UX aspects. Let's get started!",
      'assignment'
    );
    await emitChatMessage(executionId, assignmentMessage, 1);
    await delay(1500);

    // 3. Code specialist acknowledges
    const codePersonality = getSpecialistPersonality('code-specialist');
    const codeAckMessage = createSpecialistMessage(
      'code-specialist',
      codePersonality.name,
      "✅ Got it! I'll handle the technical implementation. Just to clarify - are we targeting React or Vue for this component?",
      'acknowledgment'
    );
    await emitChatMessage(executionId, codeAckMessage, 1);
    await delay(1000);

    // 4. Design specialist acknowledges
    const designPersonality = getSpecialistPersonality('design-specialist');
    const designAckMessage = createSpecialistMessage(
      'design-specialist',
      designPersonality.name,
      "🎨 Perfect! I'm excited to work on the design aspects. I'll make sure it's both beautiful and user-friendly!",
      'acknowledgment'
    );
    await emitChatMessage(executionId, designAckMessage, 1);
    await delay(2000);

    // 5. Moderator responds to clarification
    const clarificationResponse = createModeratorMessage(
      "💡 Great question @Alex! The user confirmed they want React. You can proceed with React components.",
      'clarification_response',
      codeAckMessage.id
    );
    await emitChatMessage(executionId, clarificationResponse);
    await delay(1000);

    // 6. Code specialist starts working
    const codeWorkMessage = createSpecialistMessage(
      'code-specialist',
      codePersonality.name,
      "🔥 Perfect! Starting the React implementation now. I'll create a clean, reusable component with proper TypeScript types.",
      'response',
      clarificationResponse.id
    );
    await emitChatMessage(executionId, codeWorkMessage, 1);
    await delay(3000);

    // 7. Code specialist shares progress with code attachment
    const codeAttachment: ChatAttachment = {
      type: 'code',
      content: `interface ButtonProps {
  variant: 'primary' | 'secondary';
  size: 'sm' | 'md' | 'lg';
  onClick: () => void;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({ 
  variant, 
  size, 
  onClick, 
  children 
}) => {
  return (
    <button 
      className={\`btn btn-\${variant} btn-\${size}\`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};`,
      language: 'typescript',
      filename: 'Button.tsx'
    };

    const codeResultMessage = createSpecialistMessage(
      'code-specialist',
      codePersonality.name,
      "✅ Component structure complete! Here's the React component with TypeScript. @Maya, you can now style this however you'd like!",
      'result',
      undefined,
      [codeAttachment],
      { stepNumber: 1, quality: 0.95, duration: 45000 }
    );
    await emitChatMessage(executionId, codeResultMessage, 1);
    await delay(2000);

    // 8. Design specialist responds
    const designResponseMessage = createSpecialistMessage(
      'design-specialist',
      designPersonality.name,
      "🎉 Excellent work @Alex! I love the clean structure. I'll add some beautiful Tailwind classes and make it responsive. The user experience will be amazing!",
      'response',
      codeResultMessage.id
    );
    await emitChatMessage(executionId, designResponseMessage, 2);
    await delay(2500);

    // 9. Handoff message
    const handoffMessage = createModeratorMessage(
      "🤝 Beautiful collaboration team! @Maya, you're up next. Take @Alex's component and add your design magic!",
      'handoff'
    );
    await emitChatMessage(executionId, handoffMessage, 2);
    await delay(1000);

    console.log('🎉 [Chat Test] Sample chat messages emitted successfully!');
    
  } catch (error) {
    console.error('❌ [Chat Test] Error emitting sample messages:', error);
  }
}

// Test function to simulate typing indicators
export async function simulateTyping(executionId: string, roleId: string, duration: number = 3000): Promise<void> {
  // This would emit typing events - for now just log
  console.log(`⌨️ [Chat Test] ${roleId} is typing for ${duration}ms...`);
  
  // In a real implementation, this would emit specialist_typing events
  // await emitOrchestrationEvent(executionId, 'specialist_typing', { roleId }, undefined, roleId);
}
