"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AITeamOrchestrator */ \"(app-pages-browser)/./src/components/AITeamOrchestrator.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Chat test panel state (Phase 1 testing)\n    const [showChatTestPanel, setShowChatTestPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1954,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1955,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1959,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1960,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1951,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1971,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 1973,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1965,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1980,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1979,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1964,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1950,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Streaming\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1990,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUseStreaming(!useStreaming),\n                                                        className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1997,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1991,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1989,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Chat Test\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2007,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            console.log(\"\\uD83E\\uDDEA Chat Test Panel toggle clicked! Current state:\", showChatTestPanel);\n                                                            setShowChatTestPanel(!showChatTestPanel);\n                                                        },\n                                                        className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 shadow-sm \".concat(showChatTestPanel ? \"bg-blue-500 shadow-blue-200\" : \"bg-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(showChatTestPanel ? \"translate-x-6\" : \"translate-x-1\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2017,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2008,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2006,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1987,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 1948,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 1947,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1943,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2037,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2038,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2036,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2055,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2059,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2062,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2058,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2054,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2069,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2068,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2067,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2046,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2044,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2035,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2034,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2033,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full max-w-4xl h-full overflow-y-auto px-6\",\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2091,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2090,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2106,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2109,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2110,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2111,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2108,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2107,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2105,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2103,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2127,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2126,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2125,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-[65%] relative \".concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2145,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2157,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2152,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2144,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2165,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2172,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2164,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2185,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2199,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2200,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2194,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2206,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2207,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2202,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2193,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2210,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2184,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2221,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2229,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2237,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2181,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2132,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2254,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2253,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2252,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2120,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__.AITeamOrchestrator, {\n                                                        executionId: orchestrationExecutionId,\n                                                        onComplete: (result)=>{\n                                                            console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                            // Add the final result as a message\n                                                            const finalMessage = {\n                                                                id: Date.now().toString() + \"-orchestration-final\",\n                                                                role: \"assistant\",\n                                                                content: [\n                                                                    {\n                                                                        type: \"text\",\n                                                                        text: result\n                                                                    }\n                                                                ]\n                                                            };\n                                                            setMessages((prevMessages)=>[\n                                                                    ...prevMessages,\n                                                                    finalMessage\n                                                                ]);\n                                                            // Hide orchestration UI\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                            // Save final message\n                                                            if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                                saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                    console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                                });\n                                                            }\n                                                        },\n                                                        onError: (error)=>{\n                                                            console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                            setError(\"Orchestration error: \".concat(error));\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2087,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2082,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2081,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2326,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2325,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2320,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2319,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2080,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2030,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2347,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2349,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2345,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2344,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2360,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2361,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2359,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2365,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2358,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2377,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2382,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2389,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2383,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2376,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2392,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2375,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2373,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2357,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2428,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2430,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2438,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2472,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2471,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2475,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2463,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2341,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2340,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1938,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2501,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2500,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2505,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2506,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2504,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2515,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2514,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2497,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2527,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2522,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2521,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2540,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2541,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2539,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2537,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2549,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2548,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2547,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2552,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2553,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2546,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2558,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2535,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2575,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2574,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2573,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2572,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2586,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2587,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2585,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2495,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2489,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2609,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2608,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2603,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2600,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1936,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"qKgFoes6DbHHaIfcNUGnuPJ2zgo=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});