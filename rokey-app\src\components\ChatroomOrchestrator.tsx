'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useOrchestrationStream } from '@/hooks/useOrchestrationStream';
import { ChatroomMessage, OrchestrationEvent } from '@/utils/orchestrationUtils';
import { getSpecialistPersonality, getRandomPhrase } from '@/config/specialistPersonalities';
import { SpecialistMessage } from './SpecialistMessage';
import { ModeratorMessage } from './ModeratorMessage';
import { TypingIndicator } from './TypingIndicator';
import { 
  ChatBubbleLeftRightIcon,
  UsersIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface ChatroomOrchestratorProps {
  executionId: string;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
}

export const ChatroomOrchestrator: React.FC<ChatroomOrchestratorProps> = ({
  executionId,
  onComplete,
  onError
}) => {
  const [messages, setMessages] = useState<ChatroomMessage[]>([]);
  const [typingIndicators, setTypingIndicators] = useState<Set<string>>(new Set());
  const [activeSpecialists, setActiveSpecialists] = useState<string[]>([]);
  const [isComplete, setIsComplete] = useState(false);
  const [finalResult, setFinalResult] = useState<string>('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Use the orchestration stream hook
  const {
    events,
    isConnected,
    error: streamError,
    lastEvent
  } = useOrchestrationStream(executionId);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Convert orchestration events to chatroom messages
  useEffect(() => {
    if (!lastEvent) return;

    const convertEventToMessage = (event: OrchestrationEvent): ChatroomMessage | null => {
      const personality = getSpecialistPersonality(event.role_id || 'moderator');
      
      switch (event.type) {
        case 'orchestration_started':
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: 'moderator',
            senderName: 'Alex (Moderator)',
            messageType: 'assignment',
            content: `Welcome team! We have a new request to tackle: "${event.data.originalPrompt?.substring(0, 200)}..."`,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: 0
            }
          };

        case 'task_decomposed':
          const specialists = event.data.roles?.map((r: any) => 
            getSpecialistPersonality(r.roleId)?.name || r.roleId
          ).join(', ') || 'specialists';
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: 'moderator',
            senderName: 'Alex (Moderator)',
            messageType: 'assignment',
            content: `I've analyzed the request and I'm assigning this to: ${specialists}. Let's collaborate to deliver an amazing result! 🚀`,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: 0
            }
          };

        case 'step_assigned':
          const assignedPersonality = getSpecialistPersonality(event.role_id || '');
          if (!assignedPersonality) return null;
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: 'moderator',
            senderName: 'Alex (Moderator)',
            messageType: 'assignment',
            content: `@${assignedPersonality.name}, you're up! Here's what I need: ${event.data.step?.prompt || 'Handle your specialty for this request.'}`,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: event.step_number
            }
          };

        case 'step_started':
          const workingPersonality = getSpecialistPersonality(event.role_id || '');
          if (!workingPersonality) return null;
          
          const acknowledgment = getRandomPhrase(workingPersonality.communicationStyle.acknowledgment);
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: event.role_id || '',
            senderName: workingPersonality.name,
            messageType: 'acknowledgment',
            content: acknowledgment,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: event.step_number
            }
          };

        case 'step_progress':
          const progressPersonality = getSpecialistPersonality(event.role_id || '');
          if (!progressPersonality) return null;
          
          const workingIndicator = getRandomPhrase(progressPersonality.communicationStyle.workingIndicators);
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: event.role_id || '',
            senderName: progressPersonality.name,
            messageType: 'work_update',
            content: workingIndicator,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: event.step_number,
              progress: event.data.progress,
              isStreaming: true
            }
          };

        case 'step_completed':
          const completedPersonality = getSpecialistPersonality(event.role_id || '');
          if (!completedPersonality) return null;
          
          const completionPhrase = getRandomPhrase(completedPersonality.communicationStyle.completionPhrases);
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: event.role_id || '',
            senderName: completedPersonality.name,
            messageType: 'final_output',
            content: `${completionPhrase}\n\n${event.data.output || 'Work completed successfully!'}`,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: event.step_number
            }
          };

        case 'synthesis_started':
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: 'moderator',
            senderName: 'Alex (Moderator)',
            messageType: 'assignment',
            content: 'Outstanding teamwork everyone! 🎉 Let me compile all your excellent work into a final, cohesive solution.',
            timestamp: event.timestamp,
            metadata: {
              stepNumber: 999
            }
          };

        case 'orchestration_completed':
          setIsComplete(true);
          setFinalResult(event.data.finalResult || '');
          if (onComplete && event.data.finalResult) {
            onComplete(event.data.finalResult);
          }
          
          return {
            id: event.id,
            executionId: event.execution_id,
            sender: 'moderator',
            senderName: 'Alex (Moderator)',
            messageType: 'final_output',
            content: `🎉 Brilliant collaboration team! Here's our final solution:\n\n${event.data.finalResult || 'Task completed successfully!'}`,
            timestamp: event.timestamp,
            metadata: {
              stepNumber: 1000
            }
          };

        default:
          return null;
      }
    };

    const newMessage = convertEventToMessage(lastEvent);
    if (newMessage) {
      setMessages(prev => {
        // Avoid duplicates
        if (prev.some(msg => msg.id === newMessage.id)) {
          return prev;
        }
        return [...prev, newMessage];
      });

      // Update active specialists
      if (newMessage.sender !== 'moderator' && !activeSpecialists.includes(newMessage.sender)) {
        setActiveSpecialists(prev => [...prev, newMessage.sender]);
      }
    }

    // Handle typing indicators
    if (lastEvent.type === 'step_started' && lastEvent.role_id) {
      setTypingIndicators(prev => new Set([...prev, lastEvent.role_id!]));
    } else if (lastEvent.type === 'step_completed' && lastEvent.role_id) {
      setTypingIndicators(prev => {
        const newSet = new Set(prev);
        newSet.delete(lastEvent.role_id!);
        return newSet;
      });
    }
  }, [lastEvent, activeSpecialists, onComplete]);

  // Handle stream errors
  useEffect(() => {
    if (streamError && onError) {
      onError(streamError);
    }
  }, [streamError, onError]);

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 h-[600px] flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <ChatBubbleLeftRightIcon className="w-6 h-6" />
          <div>
            <h3 className="text-lg font-semibold">AI Team Collaboration</h3>
            <p className="text-blue-100 text-sm">
              {isComplete ? 'Session Complete' : isConnected ? 'Live Session' : 'Connecting...'}
            </p>
          </div>
          <div className="flex-1" />
          <div className="flex items-center space-x-2">
            <UsersIcon className="w-5 h-5" />
            <span className="text-sm">{activeSpecialists.length + 1} members</span>
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
      >
        {messages.map((message) => (
          message.sender === 'moderator' ? (
            <ModeratorMessage
              key={message.id}
              message={message}
            />
          ) : (
            <SpecialistMessage
              key={message.id}
              message={message}
            />
          )
        ))}

        {/* Typing Indicators */}
        {Array.from(typingIndicators).map(roleId => (
          <TypingIndicator
            key={`typing-${roleId}`}
            roleId={roleId}
          />
        ))}

        <div ref={messagesEndRef} />
      </div>

      {/* Footer */}
      <div className="p-3 bg-gray-100 rounded-b-lg border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="w-4 h-4" />
            <span>
              {isComplete 
                ? 'Collaboration complete!' 
                : `${messages.length} messages • ${activeSpecialists.length} specialists active`
              }
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
