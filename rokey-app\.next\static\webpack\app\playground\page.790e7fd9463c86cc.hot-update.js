"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx":
/*!*************************************************!*\
  !*** ./src/components/ChatroomOrchestrator.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatroomOrchestrator: function() { return /* binding */ ChatroomOrchestrator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/specialistPersonalities */ \"(app-pages-browser)/./src/config/specialistPersonalities.ts\");\n/* harmony import */ var _SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SpecialistMessage */ \"(app-pages-browser)/./src/components/SpecialistMessage.tsx\");\n/* harmony import */ var _ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModeratorMessage */ \"(app-pages-browser)/./src/components/ModeratorMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatroomOrchestrator auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatroomOrchestrator = (param)=>{\n    let { executionId, onComplete, onError } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingIndicators, setTypingIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [activeSpecialists, setActiveSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use the orchestration stream hook\n    const { events, isConnected, error: streamError, lastEvent } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Auto-scroll to bottom when new messages arrive\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    // Convert orchestration events to chatroom messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lastEvent) return;\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCE8 New event received:\", lastEvent.type);\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCCB Event data:\", lastEvent);\n        const convertEventToMessage = (event)=>{\n            const personality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"moderator\");\n            switch(event.type){\n                case \"orchestration_started\":\n                    var _event_data_originalPrompt;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: 'Welcome team! We have a new request to tackle: \"'.concat((_event_data_originalPrompt = event.data.originalPrompt) === null || _event_data_originalPrompt === void 0 ? void 0 : _event_data_originalPrompt.substring(0, 200), '...\"'),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"task_decomposed\":\n                    var _event_data_roles;\n                    const specialists = ((_event_data_roles = event.data.roles) === null || _event_data_roles === void 0 ? void 0 : _event_data_roles.map((r)=>{\n                        var _getSpecialistPersonality;\n                        return ((_getSpecialistPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(r.roleId)) === null || _getSpecialistPersonality === void 0 ? void 0 : _getSpecialistPersonality.name) || r.roleId;\n                    }).join(\", \")) || \"specialists\";\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"I've analyzed the request and I'm assigning this to: \".concat(specialists, \". Let's collaborate to deliver an amazing result! \\uD83D\\uDE80\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"step_assigned\":\n                    var _event_data_step;\n                    const assignedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!assignedPersonality) return null;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"@\".concat(assignedPersonality.name, \", you're up! Here's what I need: \").concat(((_event_data_step = event.data.step) === null || _event_data_step === void 0 ? void 0 : _event_data_step.prompt) || \"Handle your specialty for this request.\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_started\":\n                    const workingPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!workingPersonality) return null;\n                    const acknowledgment = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(workingPersonality.communicationStyle.acknowledgment);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: workingPersonality.name,\n                        messageType: \"acknowledgment\",\n                        content: acknowledgment,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_progress\":\n                    const progressPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!progressPersonality) return null;\n                    const workingIndicator = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(progressPersonality.communicationStyle.workingIndicators);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: progressPersonality.name,\n                        messageType: \"work_update\",\n                        content: workingIndicator,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number,\n                            progress: event.data.progress,\n                            isStreaming: true\n                        }\n                    };\n                case \"step_completed\":\n                    const completedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!completedPersonality) return null;\n                    const completionPhrase = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(completedPersonality.communicationStyle.completionPhrases);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: completedPersonality.name,\n                        messageType: \"final_output\",\n                        content: \"\".concat(completionPhrase, \"\\n\\n\").concat(event.data.output || \"Work completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"synthesis_started\":\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"Outstanding teamwork everyone! \\uD83C\\uDF89 Let me compile all your excellent work into a final, cohesive solution.\",\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 999\n                        }\n                    };\n                case \"orchestration_completed\":\n                    setIsComplete(true);\n                    setFinalResult(event.data.finalResult || \"\");\n                    if (onComplete && event.data.finalResult) {\n                        onComplete(event.data.finalResult);\n                    }\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"final_output\",\n                        content: \"\\uD83C\\uDF89 Brilliant collaboration team! Here's our final solution:\\n\\n\".concat(event.data.finalResult || \"Task completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 1000\n                        }\n                    };\n                default:\n                    return null;\n            }\n        };\n        const newMessage = convertEventToMessage(lastEvent);\n        if (newMessage) {\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] ✅ Created new message:\", newMessage.messageType);\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDC64 From:\", newMessage.senderName);\n            console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCAD Content preview:\", newMessage.content.substring(0, 100) + \"...\");\n            setMessages((prev)=>{\n                // Avoid duplicates\n                if (prev.some((msg)=>msg.id === newMessage.id)) {\n                    console.log(\"\\uD83D\\uDCAC [CHATROOM] ⚠️ Duplicate message detected, skipping\");\n                    return prev;\n                }\n                console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCDD Adding message to chat, total messages:\", prev.length + 1);\n                return [\n                    ...prev,\n                    newMessage\n                ];\n            });\n            // Update active specialists\n            if (newMessage.sender !== \"moderator\" && !activeSpecialists.includes(newMessage.sender)) {\n                setActiveSpecialists((prev)=>[\n                        ...prev,\n                        newMessage.sender\n                    ]);\n            }\n        }\n        // Handle typing indicators\n        if (lastEvent.type === \"step_started\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>new Set([\n                    ...prev,\n                    lastEvent.role_id\n                ]));\n        } else if (lastEvent.type === \"step_completed\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(lastEvent.role_id);\n                return newSet;\n            });\n        }\n    }, [\n        lastEvent,\n        activeSpecialists,\n        onComplete\n    ]);\n    // Handle stream errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (streamError && onError) {\n            onError(streamError);\n        }\n    }, [\n        streamError,\n        onError\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg border border-gray-200 h-[600px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"AI Team Collaboration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 text-sm\",\n                                    children: isComplete ? \"Session Complete\" : isConnected ? \"Live Session\" : \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        activeSpecialists.length + 1,\n                                        \" members\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\",\n                children: [\n                    messages.map((message)=>message.sender === \"moderator\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__.ModeratorMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__.SpecialistMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, undefined)),\n                    Array.from(typingIndicators).map((roleId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__.TypingIndicator, {\n                            roleId: roleId\n                        }, \"typing-\".concat(roleId), false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-gray-100 rounded-b-lg border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isComplete ? \"Collaboration complete!\" : \"\".concat(messages.length, \" messages • \").concat(activeSpecialists.length, \" specialists active\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-red-500\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isConnected ? \"Connected\" : \"Disconnected\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatroomOrchestrator, \"cL5azj1dJTnwRx7QRF6uCCEzqAU=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = ChatroomOrchestrator;\nvar _c;\n$RefreshReg$(_c, \"ChatroomOrchestrator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx\n"));

/***/ })

});