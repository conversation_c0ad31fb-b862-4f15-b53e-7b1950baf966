"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AITeamOrchestrator */ \"(app-pages-browser)/./src/components/AITeamOrchestrator.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Function to inject sample chat messages into the conversation\n    const injectSampleChatMessages = async ()=>{\n        console.log(\"\\uD83C\\uDFAC [CHAT MODE] Injecting sample chat messages...\");\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        try {\n            // 1. Moderator opens the session\n            const moderatorMessage1 = {\n                id: Date.now().toString() + \"-mod-1\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83C\\uDFAF **AI Moderator**: Welcome team! We have a new request from our user. Let me break this down and assign the right specialists.\\n\\n\\uD83D\\uDCCB I'm assigning this to @Alex Code for the technical implementation and @Maya Design for the UI/UX aspects. Let's get started!\"\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    moderatorMessage1\n                ]);\n            await delay(2000);\n            // 2. Code specialist acknowledges\n            const codePersonality = getSpecialistPersonality(\"code-specialist\");\n            const codeMessage1 = {\n                id: Date.now().toString() + \"-code-1\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83D\\uDCBB **\".concat(codePersonality.name, \"**: ✅ Got it! I'll handle the technical implementation. Just to clarify - are we targeting React or Vue for this component?\")\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    codeMessage1\n                ]);\n            await delay(1500);\n            // 3. Design specialist acknowledges\n            const designPersonality = getSpecialistPersonality(\"design-specialist\");\n            const designMessage1 = {\n                id: Date.now().toString() + \"-design-1\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83C\\uDFA8 **\".concat(designPersonality.name, \"**: Perfect! I'm excited to work on the design aspects. I'll make sure it's both beautiful and user-friendly!\")\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    designMessage1\n                ]);\n            await delay(2000);\n            // 4. Moderator responds to clarification\n            const moderatorMessage2 = {\n                id: Date.now().toString() + \"-mod-2\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83C\\uDFAF **AI Moderator**: Great question @Alex! The user confirmed they want React. You can proceed with React components.\"\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    moderatorMessage2\n                ]);\n            await delay(1500);\n            // 5. Code specialist starts working\n            const codeMessage2 = {\n                id: Date.now().toString() + \"-code-2\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83D\\uDCBB **\".concat(codePersonality.name, \"**: \\uD83D\\uDD25 Perfect! Starting the React implementation now. I'll create a clean, reusable component with proper TypeScript types.\")\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    codeMessage2\n                ]);\n            await delay(3000);\n            // 6. Code specialist shares result with code\n            const codeMessage3 = {\n                id: Date.now().toString() + \"-code-3\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83D\\uDCBB **\".concat(codePersonality.name, \"**: ✅ Component structure complete! Here's the React component with TypeScript:\\n\\n```typescript\\ninterface ButtonProps {\\n  variant: 'primary' | 'secondary';\\n  size: 'sm' | 'md' | 'lg';\\n  onClick: () => void;\\n  children: React.ReactNode;\\n}\\n\\nexport const Button: React.FC<ButtonProps> = ({\\n  variant,\\n  size,\\n  onClick,\\n  children\\n}) => {\\n  return (\\n    <button\\n      className={`btn btn-${variant} btn-${size}`}\\n      onClick={onClick}\\n    >\\n      {children}\\n    </button>\\n  );\\n};\\n```\\n\\n@Maya, you can now style this however you'd like!\")\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    codeMessage3\n                ]);\n            await delay(2500);\n            // 7. Design specialist responds\n            const designMessage2 = {\n                id: Date.now().toString() + \"-design-2\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83C\\uDFA8 **\".concat(designPersonality.name, \"**: \\uD83C\\uDF89 Excellent work @Alex! I love the clean structure. I'll add some beautiful Tailwind classes and make it responsive. The user experience will be amazing!\")\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    designMessage2\n                ]);\n            await delay(2000);\n            // 8. Handoff message\n            const moderatorMessage3 = {\n                id: Date.now().toString() + \"-mod-3\",\n                role: \"assistant\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"\\uD83C\\uDFAF **AI Moderator**: \\uD83E\\uDD1D Beautiful collaboration team! @Maya, you're up next. Take @Alex's component and add your design magic!\"\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    moderatorMessage3\n                ]);\n            console.log(\"\\uD83C\\uDF89 [CHAT MODE] Sample chat messages injected successfully!\");\n        } catch (error) {\n            console.error(\"❌ [CHAT MODE] Error injecting messages:\", error);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2088,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2089,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2093,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2094,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2085,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2105,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2107,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2114,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2112,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2098,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2084,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Streaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setUseStreaming(!useStreaming),\n                                                    className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2131,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2125,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2123,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2082,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2081,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2077,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2153,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2154,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2152,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2171,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2175,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2178,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2174,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2170,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2185,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2184,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2183,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2162,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2160,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2151,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2149,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full max-w-4xl h-full overflow-y-auto px-6\",\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2207,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2206,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2222,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2225,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2226,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2227,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2224,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2223,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2221,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2243,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2242,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2241,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-[65%] relative \".concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2261,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2273,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2268,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2260,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2281,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2288,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2280,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2301,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2315,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2316,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2310,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2322,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2323,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2318,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2309,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2326,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2300,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2337,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2345,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2353,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2297,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2248,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2370,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2369,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2368,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2236,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2378,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__.AITeamOrchestrator, {\n                                                        executionId: orchestrationExecutionId,\n                                                        onComplete: (result)=>{\n                                                            console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                            // Add the final result as a message\n                                                            const finalMessage = {\n                                                                id: Date.now().toString() + \"-orchestration-final\",\n                                                                role: \"assistant\",\n                                                                content: [\n                                                                    {\n                                                                        type: \"text\",\n                                                                        text: result\n                                                                    }\n                                                                ]\n                                                            };\n                                                            setMessages((prevMessages)=>[\n                                                                    ...prevMessages,\n                                                                    finalMessage\n                                                                ]);\n                                                            // Hide orchestration UI\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                            // Save final message\n                                                            if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                                saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                    console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                                });\n                                                            }\n                                                        },\n                                                        onError: (error)=>{\n                                                            console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                            setError(\"Orchestration error: \".concat(error));\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2391,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2203,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2197,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2442,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2441,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2436,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2435,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2196,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2463,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2462,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2465,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2461,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2460,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2477,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2475,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2481,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2493,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2498,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2505,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2499,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2492,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2508,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2491,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2521,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2544,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2546,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2554,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2588,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2587,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2591,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2579,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2519,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2470,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2457,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2452,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2072,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2616,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2615,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2621,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2622,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2620,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2614,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2631,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2630,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2625,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2613,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2643,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2642,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2645,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2638,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2637,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2656,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2657,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2655,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2653,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2665,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2664,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2663,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2668,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2669,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2662,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2674,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2651,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2691,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2690,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2689,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2688,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2702,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2703,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2701,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2700,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2611,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2605,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2725,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2724,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2719,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2716,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 2070,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"dQtgRDvqik5z8cOOvJCbcR0Oe/c=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});