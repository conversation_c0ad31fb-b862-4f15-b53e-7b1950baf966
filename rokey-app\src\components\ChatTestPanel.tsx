'use client';

import React, { useState } from 'react';
import { ChatMessage } from './ChatMessage';
import { useOrchestrationChatMessages } from '@/hooks/useOrchestrationStream';
import { emitSampleChatMessages } from '@/utils/chatTestUtils';

interface ChatTestPanelProps {
  executionId?: string;
}

export const ChatTestPanel: React.FC<ChatTestPanelProps> = ({ executionId }) => {
  const [testExecutionId, setTestExecutionId] = useState(executionId || 'test-execution-' + Date.now());
  const [isEmitting, setIsEmitting] = useState(false);
  
  const {
    messages,
    latestMessage,
    messagesByType,
    messagesBySender,
    messagesFromSpecialist,
    isSpecialistTyping
  } = useOrchestrationChatMessages(testExecutionId);

  const handleEmitSampleMessages = async () => {
    setIsEmitting(true);
    try {
      await emitSampleChatMessages(testExecutionId);
    } catch (error) {
      console.error('Error emitting sample messages:', error);
    } finally {
      setIsEmitting(false);
    }
  };

  const handleNewTestSession = () => {
    const newId = 'test-execution-' + Date.now();
    setTestExecutionId(newId);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          🧪 Chat Orchestration Test Panel - Phase 1
        </h2>
        
        <div className="flex items-center space-x-4 mb-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Test Execution ID:
            </label>
            <input
              type="text"
              value={testExecutionId}
              onChange={(e) => setTestExecutionId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="Enter execution ID"
            />
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleEmitSampleMessages}
              disabled={isEmitting}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
            >
              {isEmitting ? '⏳ Emitting...' : '🚀 Emit Sample Messages'}
            </button>
            
            <button
              onClick={handleNewTestSession}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
            >
              🔄 New Session
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{messages.length}</div>
            <div className="text-sm text-blue-800">Total Messages</div>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{messagesBySender('moderator').length}</div>
            <div className="text-sm text-green-800">Moderator</div>
          </div>
          
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{messagesBySender('specialist').length}</div>
            <div className="text-sm text-purple-800">Specialists</div>
          </div>
          
          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{messagesByType('acknowledgment').length}</div>
            <div className="text-sm text-orange-800">Acknowledgments</div>
          </div>
        </div>
      </div>

      {/* Messages Display */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
          💬 Chat Messages ({messages.length})
        </h3>
        
        {messages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">💭</div>
            <p>No messages yet. Click "Emit Sample Messages" to test the chat system!</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                showTimestamp={true}
                compact={false}
              />
            ))}
          </div>
        )}
      </div>

      {/* Latest Message Info */}
      {latestMessage && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">📨 Latest Message:</h4>
          <div className="text-sm text-gray-600">
            <p><strong>From:</strong> {latestMessage.senderName} ({latestMessage.sender})</p>
            <p><strong>Type:</strong> {latestMessage.messageType}</p>
            <p><strong>Time:</strong> {new Date(latestMessage.timestamp).toLocaleString()}</p>
            {latestMessage.roleId && <p><strong>Role:</strong> {latestMessage.roleId}</p>}
          </div>
        </div>
      )}

      {/* Debug Info */}
      <details className="mt-6">
        <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
          🔍 Debug Information
        </summary>
        <div className="mt-2 p-4 bg-gray-100 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div>
              <h5 className="font-semibold mb-2">Message Types:</h5>
              <ul className="space-y-1">
                {['announcement', 'assignment', 'acknowledgment', 'question', 'response', 'handoff', 'result'].map(type => (
                  <li key={type}>
                    {type}: {messagesByType(type as any).length}
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h5 className="font-semibold mb-2">Specialist Messages:</h5>
              <ul className="space-y-1">
                {['code-specialist', 'design-specialist', 'data-specialist'].map(roleId => (
                  <li key={roleId}>
                    {roleId}: {messagesFromSpecialist(roleId).length}
                    {isSpecialistTyping(roleId) && <span className="ml-2 text-blue-600">⌨️ typing...</span>}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </details>
    </div>
  );
};
