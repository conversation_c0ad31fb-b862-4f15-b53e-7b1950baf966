'use client';

import React from 'react';
import { ChatMessage as ChatMessageType, getSpecialistPersonality } from '@/utils/orchestrationUtils';

interface ChatMessageProps {
  message: ChatMessageType;
  showTimestamp?: boolean;
  compact?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  showTimestamp = true,
  compact = false
}) => {
  const personality = message.roleId ? getSpecialistPersonality(message.roleId) : null;
  
  // Get styling based on sender
  const getSenderStyling = () => {
    switch (message.sender) {
      case 'moderator':
        return {
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-900',
          nameColor: 'text-blue-700',
          emoji: '🎯'
        };
      case 'specialist':
        return {
          bgColor: `bg-gray-50 border-gray-200`,
          textColor: 'text-gray-900',
          nameColor: personality?.color ? `text-[${personality.color}]` : 'text-gray-700',
          emoji: personality?.emoji || '🤖'
        };
      case 'user':
        return {
          bgColor: 'bg-green-50 border-green-200',
          textColor: 'text-green-900',
          nameColor: 'text-green-700',
          emoji: '👤'
        };
      default:
        return {
          bgColor: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-900',
          nameColor: 'text-gray-700',
          emoji: '💬'
        };
    }
  };

  const styling = getSenderStyling();
  
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageTypeLabel = () => {
    switch (message.messageType) {
      case 'announcement': return '📢';
      case 'assignment': return '📋';
      case 'acknowledgment': return '✅';
      case 'question': return '❓';
      case 'response': return '💬';
      case 'handoff': return '🤝';
      case 'result': return '🎯';
      case 'clarification_request': return '🤔';
      case 'clarification_response': return '💡';
      default: return '💬';
    }
  };

  return (
    <div className={`p-3 rounded-lg border ${styling.bgColor} ${compact ? 'mb-2' : 'mb-4'}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{styling.emoji}</span>
          <span className={`font-semibold text-sm ${styling.nameColor}`}>
            {message.senderName}
          </span>
          <span className="text-xs text-gray-500">
            {getMessageTypeLabel()} {message.messageType.replace('_', ' ')}
          </span>
        </div>
        
        {showTimestamp && (
          <span className="text-xs text-gray-400">
            {formatTime(message.timestamp)}
          </span>
        )}
      </div>

      {/* Content */}
      <div className={`${styling.textColor} ${compact ? 'text-sm' : ''}`}>
        {message.content}
      </div>

      {/* Attachments */}
      {message.attachments && message.attachments.length > 0 && (
        <div className="mt-3 space-y-2">
          {message.attachments.map((attachment, index) => (
            <div key={index} className="bg-white p-2 rounded border">
              {attachment.type === 'code' && (
                <div>
                  <div className="text-xs text-gray-500 mb-1">
                    📄 Code {attachment.language && `(${attachment.language})`}
                  </div>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                    <code>{attachment.content}</code>
                  </pre>
                </div>
              )}
              {attachment.type === 'file' && (
                <div className="text-sm">
                  📎 {attachment.filename || 'File attachment'}
                </div>
              )}
              {attachment.type === 'link' && (
                <div className="text-sm">
                  🔗 <a href={attachment.url} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">
                    {attachment.content}
                  </a>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Metadata */}
      {message.metadata && (
        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
          {message.metadata.stepNumber && (
            <span>Step {message.metadata.stepNumber}</span>
          )}
          {message.metadata.confidence && (
            <span>Confidence: {Math.round(message.metadata.confidence * 100)}%</span>
          )}
          {message.metadata.quality && (
            <span>Quality: {Math.round(message.metadata.quality * 100)}%</span>
          )}
          {message.metadata.duration && (
            <span>Duration: {Math.round(message.metadata.duration / 1000)}s</span>
          )}
        </div>
      )}
    </div>
  );
};
