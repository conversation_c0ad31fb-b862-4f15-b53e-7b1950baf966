// Enhanced orchestration utilities for multi-role AI team collaboration

export interface OrchestrationEvent {
  id: string;
  execution_id: string;
  type: OrchestrationEventType;
  timestamp: string;
  data: any;
  step_number?: number;
  role_id?: string;
  model_name?: string;
}

export type OrchestrationEventType =
  | 'orchestration_started'
  | 'task_decomposed'
  | 'step_assigned'
  | 'step_started'
  | 'step_progress'
  | 'step_completed'
  | 'step_failed'
  | 'synthesis_started'
  | 'synthesis_progress'
  | 'orchestration_completed'
  | 'orchestration_failed'
  | 'moderator_commentary'
  // New chat-style event types
  | 'chat_message'
  | 'specialist_acknowledgment'
  | 'specialist_question'
  | 'moderator_response'
  | 'specialist_typing'
  | 'handoff_message'
  | 'user_clarification_request'
  | 'user_clarification_response';

export interface TaskDependency {
  stepNumber: number;
  dependsOn: number[];
  canRunInParallel: boolean;
  priority: number;
}

export interface EnhancedOrchestrationStep {
  stepNumber: number;
  roleId: string;
  prompt: string;
  dependencies: number[];
  canRunInParallel: boolean;
  priority: number;
  estimatedDuration: number;
  outputFormat?: string;
  moderatorInstructions?: string;
}

export interface OrchestrationWorkflow {
  steps: EnhancedOrchestrationStep[];
  parallelGroups: number[][];
  totalEstimatedDuration: number;
  complexityScore: number;
}

// New interfaces for chat-style orchestration
export interface ChatMessage {
  id: string;
  sender: 'moderator' | 'specialist' | 'user';
  senderName: string;
  roleId?: string; // For specialists
  content: string;
  messageType: 'announcement' | 'assignment' | 'acknowledgment' | 'question' | 'response' | 'handoff' | 'result' | 'clarification_request' | 'clarification_response';
  timestamp: string;
  replyTo?: string; // ID of message being replied to
  attachments?: ChatAttachment[];
  metadata?: {
    stepNumber?: number;
    confidence?: number;
    quality?: number;
    duration?: number;
  };
}

export interface ChatAttachment {
  type: 'code' | 'file' | 'image' | 'link';
  content: string;
  language?: string; // For code attachments
  filename?: string; // For file attachments
  url?: string; // For link attachments
}

export interface SpecialistPersonality {
  roleId: string;
  name: string;
  emoji: string;
  color: string;
  personality: string;
  communicationStyle: string;
  expertise: string[];
  catchphrases: string[];
}

export interface ChatOrchestrationState {
  messages: ChatMessage[];
  activeSpecialists: string[];
  currentStep: number;
  awaitingUserResponse: boolean;
  pendingClarifications: string[];
}

// Create a Server-Sent Events stream for orchestration updates
export function createOrchestrationEventStream(executionId: string): ReadableStream {
  const encoder = new TextEncoder();
  
  return new ReadableStream({
    start(controller) {
      // Send initial connection event
      const initialEvent: OrchestrationEvent = {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'orchestration_started',
        timestamp: new Date().toISOString(),
        data: { message: 'Orchestration stream connected' }
      };
      
      const eventData = `data: ${JSON.stringify(initialEvent)}\n\n`;
      controller.enqueue(encoder.encode(eventData));
    },
    
    cancel() {
      console.log(`[Orchestration Stream] Stream cancelled for execution ${executionId}`);
    }
  });
}

// Enhanced task decomposition with dependency analysis
export async function decomposeTaskWithDependencies(
  originalPrompt: string,
  roles: Array<{ roleId: string; confidence: number; executionOrder: number }>,
  classificationApiKey: string
): Promise<OrchestrationWorkflow> {
  // Create system prompt for intelligent task decomposition
  const decompositionPrompt = `You are an expert AI orchestrator. Analyze this request and create an optimal workflow for multiple AI specialists.

Original Request: "${originalPrompt}"

Available Roles: ${roles.map(r => `${r.roleId} (confidence: ${r.confidence})`).join(', ')}

Create a detailed workflow with:
1. Task breakdown into specific steps
2. Dependencies between steps
3. Opportunities for parallel processing
4. Estimated complexity and duration
5. Clear handoff instructions between models

Respond in JSON format:
{
  "workflow": {
    "steps": [
      {
        "stepNumber": 1,
        "roleId": "role_name",
        "prompt": "specific task for this role",
        "dependencies": [],
        "canRunInParallel": false,
        "priority": 1,
        "estimatedDuration": 30000,
        "moderatorInstructions": "how to validate and hand off results"
      }
    ],
    "parallelGroups": [[1], [2, 3], [4]],
    "totalEstimatedDuration": 120000,
    "complexityScore": 7
  },
  "reasoning": "explanation of the workflow design"
}`;

  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { role: 'system', content: 'You are an expert AI workflow orchestrator.' },
          { role: 'user', content: decompositionPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1500,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      throw new Error(`Decomposition API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('Empty decomposition response');
    }

    const parsed = JSON.parse(content);
    return parsed.workflow;
    
  } catch (error) {
    console.warn(`[Task Decomposition] Error: ${error}, falling back to simple decomposition`);
    
    // Fallback to simple sequential decomposition
    return createFallbackWorkflow(originalPrompt, roles);
  }
}

// Fallback workflow creation for when AI decomposition fails
function createFallbackWorkflow(
  originalPrompt: string,
  roles: Array<{ roleId: string; confidence: number; executionOrder: number }>
): OrchestrationWorkflow {
  const sortedRoles = [...roles].sort((a, b) => a.executionOrder - b.executionOrder);
  
  const steps: EnhancedOrchestrationStep[] = sortedRoles.map((role, index) => ({
    stepNumber: index + 1,
    roleId: role.roleId,
    prompt: index === 0 
      ? `Handle the ${role.roleId} aspect of this request: "${originalPrompt}"`
      : `Continue with the ${role.roleId} aspect based on the previous step's output: {{previousOutput}}`,
    dependencies: index === 0 ? [] : [index],
    canRunInParallel: false,
    priority: index + 1,
    estimatedDuration: 45000, // 45 seconds default
    moderatorInstructions: `Validate the ${role.roleId} output and prepare for next step`
  }));

  return {
    steps,
    parallelGroups: steps.map((_, index) => [index + 1]),
    totalEstimatedDuration: steps.length * 45000,
    complexityScore: Math.min(steps.length * 2, 10)
  };
}

// Emit orchestration event to stream
export async function emitOrchestrationEvent(
  executionId: string,
  eventType: OrchestrationEventType,
  data: any,
  stepNumber?: number,
  roleId?: string,
  modelName?: string
): Promise<void> {
  const event: OrchestrationEvent = {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: eventType,
    timestamp: new Date().toISOString(),
    data,
    step_number: stepNumber,
    role_id: roleId,
    model_name: modelName
  };

  // Store event in database for persistence and replay
  try {
    // This would be implemented with your Supabase client
    console.log(`[Orchestration Event] ${eventType}:`, event);
    
    // In a real implementation, you'd store this in the orchestration_events table
    // and broadcast to connected clients via WebSocket or SSE
    
  } catch (error) {
    console.error(`[Orchestration Event] Failed to emit ${eventType}:`, error);
  }
}

// Generate moderator commentary for entertainment value
export function generateModeratorCommentary(
  eventType: OrchestrationEventType,
  stepData: any,
  roleId?: string
): string {
  const commentaries: Partial<Record<OrchestrationEventType, string[]>> = {
    orchestration_started: [
      "🎬 Alright team, we've got an interesting challenge ahead!",
      "🚀 Let's break this down and see who's best suited for each part.",
      "🎯 Time to coordinate our AI specialists for optimal results."
    ],
    step_assigned: [
      `📋 Assigning the ${roleId} specialist to handle this part.`,
      `🎪 Our ${roleId} expert is stepping up to the plate!`,
      `⚡ Perfect match - ${roleId} is exactly what we need here.`
    ],
    step_started: [
      `🔥 ${roleId} is now working their magic...`,
      `⚙️ Watch ${roleId} tackle this challenge in real-time!`,
      `🎨 ${roleId} is crafting something special for us.`
    ],
    step_completed: [
      `✅ Excellent work from ${roleId}! Moving to the next phase.`,
      `🎉 ${roleId} delivered exactly what we needed. Handoff time!`,
      `💫 Beautiful execution by ${roleId}. The team is flowing perfectly.`
    ],
    synthesis_started: [
      "🧩 Now I'm weaving all these pieces together...",
      "🎭 Time for the grand finale - combining all our specialists' work!",
      "🌟 Watch as I synthesize these brilliant contributions into one cohesive result."
    ]
  };

  const options = commentaries[eventType] || ["🤖 Processing..."];
  return options[Math.floor(Math.random() * options.length)];
}

// Helper functions for creating chat messages
export function createChatMessage(
  sender: 'moderator' | 'specialist' | 'user',
  senderName: string,
  content: string,
  messageType: ChatMessage['messageType'],
  roleId?: string,
  replyTo?: string,
  attachments?: ChatAttachment[],
  metadata?: ChatMessage['metadata']
): ChatMessage {
  return {
    id: crypto.randomUUID(),
    sender,
    senderName,
    roleId,
    content,
    messageType,
    timestamp: new Date().toISOString(),
    replyTo,
    attachments,
    metadata
  };
}

export function createModeratorMessage(
  content: string,
  messageType: ChatMessage['messageType'] = 'announcement',
  replyTo?: string
): ChatMessage {
  return createChatMessage('moderator', 'AI Moderator', content, messageType, undefined, replyTo);
}

export function createSpecialistMessage(
  roleId: string,
  senderName: string,
  content: string,
  messageType: ChatMessage['messageType'] = 'response',
  replyTo?: string,
  attachments?: ChatAttachment[],
  metadata?: ChatMessage['metadata']
): ChatMessage {
  return createChatMessage('specialist', senderName, content, messageType, roleId, replyTo, attachments, metadata);
}

// Emit chat message as orchestration event
export async function emitChatMessage(
  executionId: string,
  message: ChatMessage,
  stepNumber?: number
): Promise<void> {
  await emitOrchestrationEvent(
    executionId,
    'chat_message',
    { message },
    stepNumber,
    message.roleId,
    message.senderName
  );
}

// Predefined specialist personalities
export const SPECIALIST_PERSONALITIES: Record<string, SpecialistPersonality> = {
  'code-specialist': {
    roleId: 'code-specialist',
    name: 'Alex Code',
    emoji: '💻',
    color: '#3B82F6', // Blue
    personality: 'Methodical and detail-oriented, loves clean code and best practices',
    communicationStyle: 'Technical but friendly, uses code examples to explain concepts',
    expertise: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'API Development'],
    catchphrases: [
      'Let me code this up for you!',
      'I see a clean solution here...',
      'This calls for some elegant code!',
      'Time to make this bulletproof!'
    ]
  },
  'design-specialist': {
    roleId: 'design-specialist',
    name: 'Maya Design',
    emoji: '🎨',
    color: '#EC4899', // Pink
    personality: 'Creative and user-focused, passionate about beautiful and functional design',
    communicationStyle: 'Visual and enthusiastic, often describes things in terms of user experience',
    expertise: ['UI/UX Design', 'CSS', 'Tailwind', 'Responsive Design', 'Accessibility'],
    catchphrases: [
      'Let\'s make this beautiful!',
      'Users will love this experience!',
      'I can see the perfect design for this...',
      'Time to add some visual magic!'
    ]
  },
  'data-specialist': {
    roleId: 'data-specialist',
    name: 'Sam Data',
    emoji: '📊',
    color: '#10B981', // Green
    personality: 'Analytical and precise, loves working with data and finding insights',
    communicationStyle: 'Fact-based and logical, uses metrics and examples to support points',
    expertise: ['Data Analysis', 'APIs', 'Database Design', 'Performance Optimization'],
    catchphrases: [
      'The data tells an interesting story...',
      'Let me analyze this properly...',
      'I can optimize this for better performance!',
      'The numbers don\'t lie!'
    ]
  },
  'security-specialist': {
    roleId: 'security-specialist',
    name: 'Jordan Security',
    emoji: '🔒',
    color: '#F59E0B', // Amber
    personality: 'Cautious and thorough, always thinking about potential vulnerabilities',
    communicationStyle: 'Careful and methodical, explains security implications clearly',
    expertise: ['Security', 'Authentication', 'Data Protection', 'Best Practices'],
    catchphrases: [
      'Security first, always!',
      'Let me check for vulnerabilities...',
      'We need to protect against...',
      'Better safe than sorry!'
    ]
  },
  'testing-specialist': {
    roleId: 'testing-specialist',
    name: 'Taylor Test',
    emoji: '🧪',
    color: '#8B5CF6', // Purple
    personality: 'Meticulous and quality-focused, ensures everything works perfectly',
    communicationStyle: 'Systematic and thorough, breaks down testing strategies step by step',
    expertise: ['Testing', 'Quality Assurance', 'Test Automation', 'Debugging'],
    catchphrases: [
      'Let\'s test this thoroughly!',
      'I found an edge case...',
      'Quality is non-negotiable!',
      'Time to break things (safely)!'
    ]
  }
};

// Get specialist personality by role ID
export function getSpecialistPersonality(roleId: string): SpecialistPersonality {
  return SPECIALIST_PERSONALITIES[roleId] || {
    roleId,
    name: `${roleId.charAt(0).toUpperCase() + roleId.slice(1)} Specialist`,
    emoji: '🤖',
    color: '#6B7280', // Gray
    personality: 'Professional and helpful AI specialist',
    communicationStyle: 'Clear and direct communication',
    expertise: [roleId],
    catchphrases: ['Let me help with this!', 'I\'m on it!', 'Working on this now...']
  };
}

// Calculate optimal execution strategy (parallel vs sequential)
export function calculateExecutionStrategy(workflow: OrchestrationWorkflow): {
  strategy: 'sequential' | 'parallel' | 'hybrid';
  estimatedSpeedup: number;
  riskLevel: 'low' | 'medium' | 'high';
} {
  const totalSteps = workflow.steps.length;
  const parallelGroups = workflow.parallelGroups.length;
  
  if (parallelGroups === totalSteps) {
    return {
      strategy: 'sequential',
      estimatedSpeedup: 1.0,
      riskLevel: 'low'
    };
  }
  
  if (parallelGroups === 1) {
    return {
      strategy: 'parallel',
      estimatedSpeedup: totalSteps * 0.7, // Assume 70% efficiency in parallel
      riskLevel: 'high'
    };
  }
  
  return {
    strategy: 'hybrid',
    estimatedSpeedup: (totalSteps / parallelGroups) * 0.8, // 80% efficiency for hybrid
    riskLevel: 'medium'
  };
}
