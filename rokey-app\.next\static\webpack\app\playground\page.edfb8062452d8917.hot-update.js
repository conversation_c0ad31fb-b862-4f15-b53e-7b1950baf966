"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AITeamOrchestrator */ \"(app-pages-browser)/./src/components/AITeamOrchestrator.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1960,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1961,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1965,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1966,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1957,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1977,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 1979,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1971,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1986,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1985,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1984,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1970,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1956,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1994,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2001,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1995,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1993,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 1954,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 1953,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1949,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2020,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2021,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2019,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2038,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2042,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2045,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2041,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2037,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2052,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2051,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2050,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2029,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2027,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2018,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2017,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2016,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full max-w-4xl h-full overflow-y-auto px-6\",\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2074,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2073,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2089,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2092,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2093,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2094,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2091,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2090,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2088,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2086,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2110,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2109,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2108,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-[65%] relative \".concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2128,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2140,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2135,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2127,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2148,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2155,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2147,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2168,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2182,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2183,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2177,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2189,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2190,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2185,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2176,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2193,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2167,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2204,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2212,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2220,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2164,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2115,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2237,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2236,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2235,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2103,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AITeamOrchestrator__WEBPACK_IMPORTED_MODULE_6__.AITeamOrchestrator, {\n                                                        executionId: orchestrationExecutionId,\n                                                        onComplete: (result)=>{\n                                                            console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                            // Add the final result as a message\n                                                            const finalMessage = {\n                                                                id: Date.now().toString() + \"-orchestration-final\",\n                                                                role: \"assistant\",\n                                                                content: [\n                                                                    {\n                                                                        type: \"text\",\n                                                                        text: result\n                                                                    }\n                                                                ]\n                                                            };\n                                                            setMessages((prevMessages)=>[\n                                                                    ...prevMessages,\n                                                                    finalMessage\n                                                                ]);\n                                                            // Hide orchestration UI\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                            // Save final message\n                                                            if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                                saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                    console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                                });\n                                                            }\n                                                        },\n                                                        onError: (error)=>{\n                                                            console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                            setError(\"Orchestration error: \".concat(error));\n                                                            setShowOrchestration(false);\n                                                            setOrchestrationExecutionId(null);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2258,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2070,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2065,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2064,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2307,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2306,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2301,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2300,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2063,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2013,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2328,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2327,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2326,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2342,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2358,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2363,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2370,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2364,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2357,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2373,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2356,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2409,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2411,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2397,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2419,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2418,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2453,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2452,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2456,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2444,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2384,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2322,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1944,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2482,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2481,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2487,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2496,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2495,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2490,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2478,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2508,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2510,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2503,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2502,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2521,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2522,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2520,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2518,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2530,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2529,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2528,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2533,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2534,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2527,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2539,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2516,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2556,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2555,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2554,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2553,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2567,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2568,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2566,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2565,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2476,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2470,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2590,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2589,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2584,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2581,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1942,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"dQtgRDvqik5z8cOOvJCbcR0Oe/c=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});