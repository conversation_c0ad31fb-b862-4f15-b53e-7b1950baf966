"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx":
/*!*************************************************!*\
  !*** ./src/components/ChatroomOrchestrator.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatroomOrchestrator: function() { return /* binding */ ChatroomOrchestrator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/specialistPersonalities */ \"(app-pages-browser)/./src/config/specialistPersonalities.ts\");\n/* harmony import */ var _SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SpecialistMessage */ \"(app-pages-browser)/./src/components/SpecialistMessage.tsx\");\n/* harmony import */ var _ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModeratorMessage */ \"(app-pages-browser)/./src/components/ModeratorMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatroomOrchestrator auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatroomOrchestrator = (param)=>{\n    let { executionId, onComplete, onError } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingIndicators, setTypingIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [activeSpecialists, setActiveSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use the orchestration stream hook\n    const { events, isConnected, error: streamError, lastEvent } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Auto-scroll to bottom when new messages arrive\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    // Convert orchestration events to chatroom messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lastEvent) return;\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCE8 New event received:\", lastEvent.type);\n        console.log(\"\\uD83D\\uDCAC [CHATROOM] \\uD83D\\uDCCB Event data:\", lastEvent);\n        const convertEventToMessage = (event)=>{\n            const personality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"moderator\");\n            switch(event.type){\n                case \"orchestration_started\":\n                    var _event_data_originalPrompt;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: 'Welcome team! We have a new request to tackle: \"'.concat((_event_data_originalPrompt = event.data.originalPrompt) === null || _event_data_originalPrompt === void 0 ? void 0 : _event_data_originalPrompt.substring(0, 200), '...\"'),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"task_decomposed\":\n                    var _event_data_roles;\n                    const specialists = ((_event_data_roles = event.data.roles) === null || _event_data_roles === void 0 ? void 0 : _event_data_roles.map((r)=>{\n                        var _getSpecialistPersonality;\n                        return ((_getSpecialistPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(r.roleId)) === null || _getSpecialistPersonality === void 0 ? void 0 : _getSpecialistPersonality.name) || r.roleId;\n                    }).join(\", \")) || \"specialists\";\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"I've analyzed the request and I'm assigning this to: \".concat(specialists, \". Let's collaborate to deliver an amazing result! \\uD83D\\uDE80\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 0\n                        }\n                    };\n                case \"step_assigned\":\n                    var _event_data_step;\n                    const assignedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!assignedPersonality) return null;\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"@\".concat(assignedPersonality.name, \", you're up! Here's what I need: \").concat(((_event_data_step = event.data.step) === null || _event_data_step === void 0 ? void 0 : _event_data_step.prompt) || \"Handle your specialty for this request.\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_started\":\n                    const workingPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!workingPersonality) return null;\n                    const acknowledgment = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(workingPersonality.communicationStyle.acknowledgment);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: workingPersonality.name,\n                        messageType: \"acknowledgment\",\n                        content: acknowledgment,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"step_progress\":\n                    const progressPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!progressPersonality) return null;\n                    const workingIndicator = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(progressPersonality.communicationStyle.workingIndicators);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: progressPersonality.name,\n                        messageType: \"work_update\",\n                        content: workingIndicator,\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number,\n                            progress: event.data.progress,\n                            isStreaming: true\n                        }\n                    };\n                case \"step_completed\":\n                    const completedPersonality = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getSpecialistPersonality)(event.role_id || \"\");\n                    if (!completedPersonality) return null;\n                    const completionPhrase = (0,_config_specialistPersonalities__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)(completedPersonality.communicationStyle.completionPhrases);\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: event.role_id || \"\",\n                        senderName: completedPersonality.name,\n                        messageType: \"final_output\",\n                        content: \"\".concat(completionPhrase, \"\\n\\n\").concat(event.data.output || \"Work completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: event.step_number\n                        }\n                    };\n                case \"synthesis_started\":\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"assignment\",\n                        content: \"Outstanding teamwork everyone! \\uD83C\\uDF89 Let me compile all your excellent work into a final, cohesive solution.\",\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 999\n                        }\n                    };\n                case \"orchestration_completed\":\n                    setIsComplete(true);\n                    setFinalResult(event.data.finalResult || \"\");\n                    if (onComplete && event.data.finalResult) {\n                        onComplete(event.data.finalResult);\n                    }\n                    return {\n                        id: event.id,\n                        executionId: event.execution_id,\n                        sender: \"moderator\",\n                        senderName: \"Alex (Moderator)\",\n                        messageType: \"final_output\",\n                        content: \"\\uD83C\\uDF89 Brilliant collaboration team! Here's our final solution:\\n\\n\".concat(event.data.finalResult || \"Task completed successfully!\"),\n                        timestamp: event.timestamp,\n                        metadata: {\n                            stepNumber: 1000\n                        }\n                    };\n                default:\n                    return null;\n            }\n        };\n        const newMessage = convertEventToMessage(lastEvent);\n        if (newMessage) {\n            setMessages((prev)=>{\n                // Avoid duplicates\n                if (prev.some((msg)=>msg.id === newMessage.id)) {\n                    return prev;\n                }\n                return [\n                    ...prev,\n                    newMessage\n                ];\n            });\n            // Update active specialists\n            if (newMessage.sender !== \"moderator\" && !activeSpecialists.includes(newMessage.sender)) {\n                setActiveSpecialists((prev)=>[\n                        ...prev,\n                        newMessage.sender\n                    ]);\n            }\n        }\n        // Handle typing indicators\n        if (lastEvent.type === \"step_started\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>new Set([\n                    ...prev,\n                    lastEvent.role_id\n                ]));\n        } else if (lastEvent.type === \"step_completed\" && lastEvent.role_id) {\n            setTypingIndicators((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(lastEvent.role_id);\n                return newSet;\n            });\n        }\n    }, [\n        lastEvent,\n        activeSpecialists,\n        onComplete\n    ]);\n    // Handle stream errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (streamError && onError) {\n            onError(streamError);\n        }\n    }, [\n        streamError,\n        onError\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg border border-gray-200 h-[600px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"AI Team Collaboration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 text-sm\",\n                                    children: isComplete ? \"Session Complete\" : isConnected ? \"Live Session\" : \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        activeSpecialists.length + 1,\n                                        \" members\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\",\n                children: [\n                    messages.map((message)=>message.sender === \"moderator\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModeratorMessage__WEBPACK_IMPORTED_MODULE_5__.ModeratorMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SpecialistMessage__WEBPACK_IMPORTED_MODULE_4__.SpecialistMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, undefined)),\n                    Array.from(typingIndicators).map((roleId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__.TypingIndicator, {\n                            roleId: roleId\n                        }, \"typing-\".concat(roleId), false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-gray-100 rounded-b-lg border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isComplete ? \"Collaboration complete!\" : \"\".concat(messages.length, \" messages • \").concat(activeSpecialists.length, \" specialists active\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-red-500\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isConnected ? \"Connected\" : \"Disconnected\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatroomOrchestrator.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatroomOrchestrator, \"cL5azj1dJTnwRx7QRF6uCCEzqAU=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = ChatroomOrchestrator;\nvar _c;\n$RefreshReg$(_c, \"ChatroomOrchestrator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatroomOrchestrator.tsx\n"));

/***/ })

});