"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useOrchestrationStream.ts":
/*!*********************************************!*\
  !*** ./src/hooks/useOrchestrationStream.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLatestOrchestrationEvent: function() { return /* binding */ useLatestOrchestrationEvent; },\n/* harmony export */   useOrchestrationChatMessages: function() { return /* binding */ useOrchestrationChatMessages; },\n/* harmony export */   useOrchestrationEventsByType: function() { return /* binding */ useOrchestrationEventsByType; },\n/* harmony export */   useOrchestrationProgress: function() { return /* binding */ useOrchestrationProgress; },\n/* harmony export */   useOrchestrationStream: function() { return /* binding */ useOrchestrationStream; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationStream,useOrchestrationEventsByType,useLatestOrchestrationEvent,useOrchestrationProgress,useOrchestrationChatMessages auto */ \nfunction useOrchestrationStream(executionId, directStreamUrl) {\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEvent, setLastEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [streamUrl, setStreamUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const pollingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const baseReconnectDelay = 1000; // 1 second\n    // Track the current execution ID and stream URL to detect changes\n    const currentExecutionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(\"\");\n    const currentStreamUrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(\"\");\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (eventSourceRef.current) {\n            eventSourceRef.current.close();\n            eventSourceRef.current = null;\n        }\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n            reconnectTimeoutRef.current = null;\n        }\n        setIsConnected(false);\n    }, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!executionId && !directStreamUrl) {\n            setError(\"No execution ID or direct stream URL provided\");\n            return;\n        }\n        // Determine the URL to connect to\n        const url = directStreamUrl || (executionId ? \"/api/orchestration/stream/\".concat(executionId) : \"\");\n        if (!url) {\n            setError(\"No valid stream URL could be determined\");\n            return;\n        }\n        // Skip if we're already connected to this URL\n        if (currentStreamUrlRef.current === url && isConnected) {\n            console.log(\"[Orchestration Stream] Already connected to: \".concat(url));\n            return;\n        }\n        console.log(\"[Orchestration Stream] Connecting to: \".concat(url));\n        // Clean up existing connection\n        disconnect();\n        // Update refs\n        currentExecutionIdRef.current = executionId || \"\";\n        currentStreamUrlRef.current = url;\n        try {\n            const eventSource = new EventSource(url);\n            eventSourceRef.current = eventSource;\n            eventSource.onopen = ()=>{\n                console.log(\"[Orchestration Stream] Connected to execution \".concat(executionId));\n                setIsConnected(true);\n                setError(null);\n                reconnectAttempts.current = 0;\n            };\n            eventSource.onmessage = (event)=>{\n                try {\n                    const orchestrationEvent = JSON.parse(event.data);\n                    console.log(\"[Orchestration Stream] Received event:\", orchestrationEvent);\n                    setEvents((prev)=>[\n                            ...prev,\n                            orchestrationEvent\n                        ]);\n                    setLastEvent(orchestrationEvent);\n                    // Reset error state on successful message\n                    setError(null);\n                } catch (parseError) {\n                    console.error(\"[Orchestration Stream] Error parsing event:\", parseError);\n                    setError(\"Error parsing stream data\");\n                }\n            };\n            // Handle specific event types\n            eventSource.addEventListener(\"orchestration_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Orchestration started:\", data);\n            });\n            eventSource.addEventListener(\"step_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step started:\", data);\n            });\n            eventSource.addEventListener(\"step_progress\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step progress:\", data);\n            });\n            eventSource.addEventListener(\"step_completed\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step completed:\", data);\n            });\n            eventSource.addEventListener(\"synthesis_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Synthesis started:\", data);\n            });\n            eventSource.addEventListener(\"orchestration_completed\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Orchestration completed:\", data);\n            });\n            eventSource.onerror = (event)=>{\n                console.error(\"[Orchestration Stream] Connection error:\", event);\n                setIsConnected(false);\n                // Attempt to reconnect with exponential backoff\n                if (reconnectAttempts.current < maxReconnectAttempts) {\n                    const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);\n                    reconnectAttempts.current++;\n                    setError(\"Connection lost. Reconnecting in \".concat(delay / 1000, \"s... (attempt \").concat(reconnectAttempts.current, \"/\").concat(maxReconnectAttempts, \")\"));\n                    reconnectTimeoutRef.current = setTimeout(()=>{\n                        console.log(\"[Orchestration Stream] Reconnecting... (attempt \".concat(reconnectAttempts.current, \")\"));\n                        connect();\n                    }, delay);\n                } else {\n                    setError(\"Connection failed after multiple attempts. Please refresh the page.\");\n                }\n            };\n        } catch (connectionError) {\n            console.error(\"[Orchestration Stream] Failed to create connection:\", connectionError);\n            setError(\"Failed to establish connection\");\n            setIsConnected(false);\n        }\n    }, [\n        executionId,\n        disconnect\n    ]);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        reconnectAttempts.current = 0;\n        connect();\n    }, [\n        connect\n    ]);\n    // Connect on mount and when executionId or directStreamUrl changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (executionId || directStreamUrl) {\n            connect();\n        }\n        // Cleanup on unmount\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        executionId,\n        directStreamUrl,\n        connect,\n        disconnect\n    ]);\n    // Handle page visibility changes to reconnect when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\" && !isConnected && (executionId || directStreamUrl)) {\n                console.log(\"[Orchestration Stream] Page became visible, attempting to reconnect...\");\n                reconnect();\n            }\n        };\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n        };\n    }, [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    // Handle online/offline events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleOnline = ()=>{\n            if (!isConnected && (executionId || directStreamUrl)) {\n                console.log(\"[Orchestration Stream] Network came back online, attempting to reconnect...\");\n                reconnect();\n            }\n        };\n        const handleOffline = ()=>{\n            setError(\"Network connection lost\");\n        };\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    return {\n        events,\n        isConnected,\n        error,\n        lastEvent,\n        reconnect,\n        disconnect\n    };\n}\n// Helper hook for filtering events by type\nfunction useOrchestrationEventsByType(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    return events.filter((event)=>event.type === eventType);\n}\n// Helper hook for getting the latest event of a specific type\nfunction useLatestOrchestrationEvent(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    const filteredEvents = events.filter((event)=>event.type === eventType);\n    return filteredEvents.length > 0 ? filteredEvents[filteredEvents.length - 1] : null;\n}\n// Helper hook for tracking orchestration progress\nfunction useOrchestrationProgress(executionId) {\n    const { events } = useOrchestrationStream(executionId);\n    const stepStartedEvents = events.filter((e)=>e.type === \"step_started\");\n    const stepCompletedEvents = events.filter((e)=>e.type === \"step_completed\");\n    const orchestrationCompleted = events.some((e)=>e.type === \"orchestration_completed\");\n    const totalSteps = stepStartedEvents.length;\n    const completedSteps = stepCompletedEvents.length;\n    const currentStep = totalSteps > 0 ? Math.min(completedSteps + 1, totalSteps) : 0;\n    const progress = totalSteps > 0 ? completedSteps / totalSteps * 100 : 0;\n    return {\n        totalSteps,\n        completedSteps,\n        currentStep,\n        progress,\n        isComplete: orchestrationCompleted\n    };\n}\n// Helper hook for chat messages\nfunction useOrchestrationChatMessages(executionId) {\n    const { events } = useOrchestrationStream(executionId);\n    // Extract chat messages from events\n    const chatMessages = events.filter((event)=>{\n        var _event_data;\n        return event.type === \"chat_message\" && ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.message);\n    }).map((event)=>event.data.message);\n    // Get typing indicators\n    const typingEvents = events.filter((event)=>event.type === \"specialist_typing\");\n    const messagesByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((messageType)=>{\n        return chatMessages.filter((msg)=>msg.messageType === messageType);\n    }, [\n        chatMessages\n    ]);\n    const messagesBySender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((sender)=>{\n        return chatMessages.filter((msg)=>msg.sender === sender);\n    }, [\n        chatMessages\n    ]);\n    const messagesFromSpecialist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((roleId)=>{\n        return chatMessages.filter((msg)=>msg.sender === \"specialist\" && msg.roleId === roleId);\n    }, [\n        chatMessages\n    ]);\n    const isSpecialistTyping = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((roleId)=>{\n        // Check if there's a recent typing event for this specialist\n        const recentTypingEvent = typingEvents.filter((event)=>event.role_id === roleId).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];\n        if (!recentTypingEvent) return false;\n        // Consider typing if the event is less than 10 seconds old\n        const eventTime = new Date(recentTypingEvent.timestamp).getTime();\n        const now = Date.now();\n        return now - eventTime < 10000; // 10 seconds\n    }, [\n        typingEvents\n    ]);\n    return {\n        messages: chatMessages,\n        latestMessage: chatMessages.length > 0 ? chatMessages[chatMessages.length - 1] : null,\n        messagesByType,\n        messagesBySender,\n        messagesFromSpecialist,\n        isSpecialistTyping\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\n"));

/***/ })

});